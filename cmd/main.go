package main

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v4/pgxpool"

	"github.com/izy-mercado/backend/internal/config"
	server "github.com/izy-mercado/backend/internal/http"
	"github.com/izy-mercado/backend/internal/integrations/storage"
)

var version = "dev" // Default if not set via -ldflags //

func main() {
	fmt.Println("App Version:", version)
	// load environment variables
	env := config.Must()
	ctx := context.Background()

	// init postgres connection
	pool := setupPostgres(env.Core.DATABASE_URL, ctx)

	storage, err := storage.New(env.Storage)
	if err != nil {
		log.Fatal("failed to initialize storage:", err)
	}

	// start http server
	server.New(env, pool, storage).Start()
}

// connect to postgres pool
func setupPostgres(databaseURL string, ctx context.Context) *pgxpool.Pool {
	// Parse the database URL
	conf, err := pgxpool.ParseConfig(databaseURL)
	if err != nil {
		log.Fatalf("Unable to parse database url: %v", err)
	}

	if caPEM := os.Getenv("DATABASE_SSL_SERVER_CA"); caPEM != "" {
		roots := x509.NewCertPool()
		if ok := roots.AppendCertsFromPEM([]byte(caPEM)); !ok {
			log.Fatalf("Failed to parse DATABASE_SSL_SERVER_CA")
		}

		tlsConfig := &tls.Config{
			MinVersion:         tls.VersionTLS12,
			RootCAs:            roots,
			InsecureSkipVerify: true,
		}
		tlsConfig.VerifyPeerCertificate = func(rawCerts [][]byte, _ [][]*x509.Certificate) error {
			if len(rawCerts) == 0 {
				return fmt.Errorf("no server certificates provided")
			}
			leaf, err := x509.ParseCertificate(rawCerts[0])
			if err != nil {
				return fmt.Errorf("failed to parse server certificate: %w", err)
			}
			opts := x509.VerifyOptions{Roots: roots}
			if _, err := leaf.Verify(opts); err != nil {
				return fmt.Errorf("server certificate verification failed: %w", err)
			}
			return nil
		}

		if certPEM, keyPEM := os.Getenv("DATABASE_SSL_CLIENT_CERT"), os.Getenv("DATABASE_SSL_CLIENT_KEY"); certPEM != "" && keyPEM != "" {
			pair, err := tls.X509KeyPair([]byte(certPEM), []byte(keyPEM))
			if err != nil {
				log.Fatalf("Invalid client certificate/key: %v", err)
			}
			tlsConfig.Certificates = []tls.Certificate{pair}
		}

		if conf.ConnConfig != nil {
			conf.ConnConfig.TLSConfig = tlsConfig
		}
	}

	// Configure connection pool settings
	conf.MaxConns = 20
	conf.MinConns = 5

	pool, err := pgxpool.ConnectConfig(ctx, conf)
	if err != nil {
		log.Fatalf("Unable to connect to database: %v", err)
	}

	log.Printf("PostgreSQL connection pool established successfully (MaxConns: %d, MinConns: %d)",
		conf.MaxConns, conf.MinConns)

	return pool
}
