-- Create template products list table if it doesn't exist
CREATE TABLE IF NOT EXISTS templates_products_lists (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    external_id VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create template products list items table if it doesn't exist
CREATE TABLE IF NOT EXISTS templates_products_lists_items (
    id SERIAL PRIMARY KEY,
    template_id INTEGER REFERENCES templates_products_lists(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Insert template only if it doesn't exist
INSERT INTO templates_products_lists (name, external_id)
SELECT 'Compras do mês', '01JR0YDFS87ZNSH4DSCER8DJ<PERSON>'
WHERE NOT EXISTS (
    SELECT 1 FROM templates_products_lists
    WHERE external_id = '01JR0YDFS87ZNSH4DSCER8DJBA'
);

-- Insert template items only if products exist and template items don't already exist
WITH template AS (
  SELECT id FROM templates_products_lists WHERE external_id = '01JR0YDFS87ZNSH4DSCER8DJBA'
),
existing_products AS (
  SELECT id as product_id FROM products WHERE id IN (2722, 2734, 2719, 2737, 2733, 2736)
)
INSERT INTO templates_products_lists_items (template_id, product_id, quantity)
SELECT
  t.id,
  ep.product_id,
  CASE ep.product_id
    WHEN 2722 THEN 2
    ELSE 1
  END as quantity
FROM template t
CROSS JOIN existing_products ep
WHERE NOT EXISTS (
    SELECT 1 FROM templates_products_lists_items tpli
    WHERE tpli.template_id = t.id AND tpli.product_id = ep.product_id
);