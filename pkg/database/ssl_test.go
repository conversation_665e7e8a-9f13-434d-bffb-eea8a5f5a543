package database

import (
	"os"
	"testing"
)

func TestLoadSSLConfigFromEnv(t *testing.T) {
	// Test case 1: No SSL configuration
	os.Unsetenv("DATABASE_SSL_SERVER_CA")
	os.Unsetenv("DATABASE_SSL_CLIENT_CERT")
	os.Unsetenv("DATABASE_SSL_CLIENT_KEY")
	
	config := LoadSSLConfigFromEnv()
	if config != nil {
		t.Error("Expected nil config when no SSL environment variables are set")
	}

	// Test case 2: Server CA only
	os.Setenv("DATABASE_SSL_SERVER_CA", "test-server-ca")
	defer os.Unsetenv("DATABASE_SSL_SERVER_CA")
	
	config = LoadSSLConfigFromEnv()
	if config == nil {
		t.Fatal("Expected non-nil config when SERVER_CA is set")
	}
	if config.ServerCA != "test-server-ca" {
		t.<PERSON><PERSON><PERSON>("Expected ServerCA to be 'test-server-ca', got '%s'", config.ServerCA)
	}
	if config.ClientCert != "" || config.ClientKey != "" {
		t.Error("Expected empty client cert/key when not set")
	}

	// Test case 3: Full SSL configuration
	os.Setenv("DATABASE_SSL_CLIENT_CERT", "test-client-cert")
	os.Setenv("DATABASE_SSL_CLIENT_KEY", "test-client-key")
	defer os.Unsetenv("DATABASE_SSL_CLIENT_CERT")
	defer os.Unsetenv("DATABASE_SSL_CLIENT_KEY")
	
	config = LoadSSLConfigFromEnv()
	if config == nil {
		t.Fatal("Expected non-nil config when all SSL vars are set")
	}
	if config.ServerCA != "test-server-ca" {
		t.Errorf("Expected ServerCA to be 'test-server-ca', got '%s'", config.ServerCA)
	}
	if config.ClientCert != "test-client-cert" {
		t.Errorf("Expected ClientCert to be 'test-client-cert', got '%s'", config.ClientCert)
	}
	if config.ClientKey != "test-client-key" {
		t.Errorf("Expected ClientKey to be 'test-client-key', got '%s'", config.ClientKey)
	}
}

func TestSSLConfigMethods(t *testing.T) {
	// Test nil config
	var config *SSLConfig
	if config.IsSSLEnabled() {
		t.Error("Expected IsSSLEnabled to return false for nil config")
	}
	if config.HasClientAuth() {
		t.Error("Expected HasClientAuth to return false for nil config")
	}

	// Test config with server CA only
	config = &SSLConfig{
		ServerCA: "test-ca",
	}
	if !config.IsSSLEnabled() {
		t.Error("Expected IsSSLEnabled to return true when ServerCA is set")
	}
	if config.HasClientAuth() {
		t.Error("Expected HasClientAuth to return false when client cert/key not set")
	}

	// Test config with client auth
	config = &SSLConfig{
		ServerCA:   "test-ca",
		ClientCert: "test-cert",
		ClientKey:  "test-key",
	}
	if !config.IsSSLEnabled() {
		t.Error("Expected IsSSLEnabled to return true when ServerCA is set")
	}
	if !config.HasClientAuth() {
		t.Error("Expected HasClientAuth to return true when client cert/key are set")
	}
}

func TestGetLibPQSSLMode(t *testing.T) {
	// Test nil config
	var config *SSLConfig
	mode := config.GetLibPQSSLMode()
	if mode != "disable" {
		t.Errorf("Expected 'disable' for nil config, got '%s'", mode)
	}

	// Test config without SSL
	config = &SSLConfig{}
	mode = config.GetLibPQSSLMode()
	if mode != "disable" {
		t.Errorf("Expected 'disable' for empty config, got '%s'", mode)
	}

	// Test config with SSL
	config = &SSLConfig{
		ServerCA: "test-ca",
	}
	mode = config.GetLibPQSSLMode()
	if mode != "require" {
		t.Errorf("Expected 'require' for SSL-enabled config, got '%s'", mode)
	}
}
