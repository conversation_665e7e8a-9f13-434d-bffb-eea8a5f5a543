// Package database provides database connection utilities including SSL/TLS configuration.
package database

import (
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"os"
)

// SSLConfig holds SSL/TLS configuration options for database connections.
type SSLConfig struct {
	// ServerCA is the PEM-encoded server certificate authority certificate
	Server<PERSON> string
	// ClientCert is the PEM-encoded client certificate (optional)
	ClientCert string
	// ClientKey is the PEM-encoded client private key (optional)
	ClientKey string
}

// LoadSSLConfigFromEnv loads SSL configuration from environment variables.
// It reads:
// - DATABASE_SSL_SERVER_CA: Server certificate authority (required for SSL)
// - DATABASE_SSL_CLIENT_CERT: Client certificate (optional)
// - DATABASE_SSL_CLIENT_KEY: Client private key (optional)
func LoadSSLConfigFromEnv() *SSLConfig {
	serverCA := os.Getenv("DATABASE_SSL_SERVER_CA")
	if serverCA == "" {
		return nil // No SSL configuration
	}

	return &SSLConfig{
		ServerCA:   serverCA,
		ClientCert: os.Getenv("DATABASE_SSL_CLIENT_CERT"),
		ClientKey:  os.Getenv("DATABASE_SSL_CLIENT_KEY"),
	}
}

// CreateTLSConfig creates a *tls.Config from the SSL configuration.
// It configures:
// - TLS 1.2 minimum version
// - Server certificate verification using the provided CA
// - Optional client certificate authentication
// - Custom certificate verification to handle Cloud SQL proxy scenarios
func (c *SSLConfig) CreateTLSConfig() (*tls.Config, error) {
	if c == nil || c.ServerCA == "" {
		return nil, nil // No SSL configuration
	}

	// Parse server CA certificate
	roots := x509.NewCertPool()
	if ok := roots.AppendCertsFromPEM([]byte(c.ServerCA)); !ok {
		return nil, fmt.Errorf("failed to parse DATABASE_SSL_SERVER_CA")
	}

	// Create base TLS configuration
	tlsConfig := &tls.Config{
		MinVersion:         tls.VersionTLS12,
		RootCAs:            roots,
		InsecureSkipVerify: true, // We handle verification manually below
	}

	// Custom certificate verification for Cloud SQL compatibility
	tlsConfig.VerifyPeerCertificate = func(rawCerts [][]byte, _ [][]*x509.Certificate) error {
		if len(rawCerts) == 0 {
			return fmt.Errorf("no server certificates provided")
		}

		leaf, err := x509.ParseCertificate(rawCerts[0])
		if err != nil {
			return fmt.Errorf("failed to parse server certificate: %w", err)
		}

		opts := x509.VerifyOptions{Roots: roots}
		if _, err := leaf.Verify(opts); err != nil {
			return fmt.Errorf("server certificate verification failed: %w", err)
		}

		return nil
	}

	// Add client certificate if provided
	if c.ClientCert != "" && c.ClientKey != "" {
		pair, err := tls.X509KeyPair([]byte(c.ClientCert), []byte(c.ClientKey))
		if err != nil {
			return nil, fmt.Errorf("invalid client certificate/key: %w", err)
		}
		tlsConfig.Certificates = []tls.Certificate{pair}
	}

	return tlsConfig, nil
}

// IsSSLEnabled returns true if SSL configuration is available.
func (c *SSLConfig) IsSSLEnabled() bool {
	return c != nil && c.ServerCA != ""
}

// HasClientAuth returns true if client certificate authentication is configured.
func (c *SSLConfig) HasClientAuth() bool {
	return c != nil && c.ClientCert != "" && c.ClientKey != ""
}

// GetLibPQSSLMode returns the appropriate SSL mode for lib/pq driver.
// For lib/pq, SSL configuration is typically handled through connection string parameters
// or environment variables. This function provides guidance on the SSL mode to use.
func (c *SSLConfig) GetLibPQSSLMode() string {
	if c == nil || !c.IsSSLEnabled() {
		return "disable"
	}

	// For Cloud SQL and similar managed services, "require" mode is typically sufficient
	// when the server certificate is properly configured
	return "require"
}
