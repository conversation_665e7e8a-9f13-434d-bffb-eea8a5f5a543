// Package withdrawalprocessor provides a Cloud Function for processing withdrawal payouts and refreshing balance views.
package withdrawalprocessor

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/GoogleCloudPlatform/functions-framework-go/functions"
	"github.com/cloudevents/sdk-go/v2/event"
	_ "github.com/lib/pq" // PostgreSQL driver
)

// PayoutProcessorRequest represents the Cloud Scheduler payload
type PayoutProcessorRequest struct {
	Action string `json:"action"` // "update_payouts" or "refresh_balance_view"
}

// PubSubMessage represents the Pub/Sub message structure
type PubSubMessage struct {
	Message struct {
		Data      string `json:"data"` // base64 encoded payload
		MessageID string `json:"messageId"`
	} `json:"message"`
	Subscription string `json:"subscription"`
}

// PayoutProcessorResponse represents the function response
type PayoutProcessorResponse struct {
	Success       bool   `json:"success"`
	UpdatedCount  int    `json:"updated_count,omitempty"`
	Message       string `json:"message"`
	ExecutionTime string `json:"execution_time"`
	Timestamp     string `json:"timestamp"`
}

func init() {
	functions.CloudEvent("ProcessWithdrawalPayouts", processWithdrawalPayouts)
}

// processWithdrawalPayouts is the Cloud Function entry point for Pub/Sub events
func processWithdrawalPayouts(ctx context.Context, e event.Event) error {
	start := time.Now()

	log.Printf("Processing Pub/Sub event: ID=%s, Type=%s, Source=%s, Data=%s", e.ID(), e.Type(), e.Source(), string(e.Data()))

	// Parse the Pub/Sub message structure
	var pubsubMsg PubSubMessage
	if err := json.Unmarshal(e.Data(), &pubsubMsg); err != nil {
		log.Printf("Failed to unmarshal Pub/Sub message structure: %v", err)
		return fmt.Errorf("invalid Pub/Sub message structure: %w", err)
	}

	// Decode the base64 encoded message data
	decodedData, err := base64.StdEncoding.DecodeString(pubsubMsg.Message.Data)
	if err != nil {
		log.Printf("Failed to decode base64 message data: %v", err)
		return fmt.Errorf("invalid base64 message data: %w", err)
	}

	log.Printf("Decoded message data: %s", string(decodedData))

	// Parse the actual payload
	var req PayoutProcessorRequest
	if err := json.Unmarshal(decodedData, &req); err != nil {
		log.Printf("Failed to unmarshal payload: %v", err)
		return fmt.Errorf("invalid payload format: %w", err)
	}

	// Validate action
	if req.Action != "update_payouts" && req.Action != "refresh_balance_view" {
		log.Printf("Invalid action: %s", req.Action)
		return fmt.Errorf("invalid action '%s': must be 'update_payouts' or 'refresh_balance_view'", req.Action)
	}

	log.Printf("Processing action: %s", req.Action)

	// Connect to database
	db, err := connectToDatabase()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		return fmt.Errorf("database connection failed: %w", err)
	}
	defer db.Close()

	var response PayoutProcessorResponse

	switch req.Action {
	case "update_payouts":
		response = updatePayoutStatuses(ctx, db)
	case "refresh_balance_view":
		response = refreshBalanceView(ctx, db)
	}

	// Add execution metadata
	response.ExecutionTime = time.Since(start).String()
	response.Timestamp = time.Now().UTC().Format(time.RFC3339)

	// Log the result
	if response.Success {
		log.Printf("Action '%s' completed successfully in %s", req.Action, response.ExecutionTime)
	} else {
		log.Printf("Action '%s' failed: %s", req.Action, response.Message)
		return fmt.Errorf("action '%s' failed: %s", req.Action, response.Message)
	}

	log.Printf("Pub/Sub message processed successfully: action=%s, execution_time=%s", req.Action, response.ExecutionTime)
	return nil
}

// connectToDatabase establishes database connection using environment variables
func connectToDatabase() (*sql.DB, error) {
	// Get database configuration from environment variables
	databaseURL := os.Getenv("DATABASE_URL")

	// Validate required environment variables
	if databaseURL == "" {
		return nil, fmt.Errorf("missing required database environment variables")
	}

	// Configure SSL if enabled
	connectionString := configureSSLForConnection(databaseURL)

	// Open database connection
	db, err := sql.Open("postgres", connectionString)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	// Configure connection pool for Cloud Function
	db.SetMaxOpenConns(5) // Limit connections for serverless
	db.SetMaxIdleConns(2) // Keep minimal idle connections
	db.SetConnMaxLifetime(5 * time.Minute)

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}

// configureSSLForConnection configures SSL parameters for the database connection string.
// This function implements the same SSL configuration logic as the shared package
// but adapted for lib/pq driver usage in Cloud Functions.
func configureSSLForConnection(baseURL string) string {
	// Check if SSL configuration is available
	serverCA := os.Getenv("DATABASE_SSL_SERVER_CA")
	if serverCA == "" {
		// No SSL configuration, ensure sslmode=disable if not already set
		if !strings.Contains(baseURL, "sslmode=") {
			separator := "?"
			if strings.Contains(baseURL, "?") {
				separator = "&"
			}
			return baseURL + separator + "sslmode=disable"
		}
		return baseURL
	}

	// SSL is configured, use require mode for Cloud SQL compatibility
	// For lib/pq with Cloud SQL, sslmode=require is typically sufficient
	// as the server certificates are properly configured
	if !strings.Contains(baseURL, "sslmode=") {
		separator := "?"
		if strings.Contains(baseURL, "?") {
			separator = "&"
		}
		return baseURL + separator + "sslmode=require"
	}

	// If sslmode is already in the URL, replace it with require
	// This ensures consistent SSL behavior
	if strings.Contains(baseURL, "sslmode=disable") {
		return strings.Replace(baseURL, "sslmode=disable", "sslmode=require", 1)
	}

	return baseURL
}

// updatePayoutStatuses updates pending payouts to available status
func updatePayoutStatuses(ctx context.Context, db *sql.DB) PayoutProcessorResponse {
	query := `
		UPDATE invoice_payouts
		SET status = 'available', updated_at = NOW()
		WHERE status = 'pending'
		  AND available_after <= NOW()
	`

	result, err := db.ExecContext(ctx, query)
	if err != nil {
		return PayoutProcessorResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update payout statuses: %v", err),
		}
	}

	rowsAffected, _ := result.RowsAffected()

	return PayoutProcessorResponse{
		Success:      true,
		UpdatedCount: int(rowsAffected),
		Message:      fmt.Sprintf("Successfully updated %d payouts from pending to available", rowsAffected),
	}
}

// refreshBalanceView refreshes the materialized view for fast balance calculations
func refreshBalanceView(ctx context.Context, db *sql.DB) PayoutProcessorResponse {
	query := `REFRESH MATERIALIZED VIEW company_available_balances`

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		return PayoutProcessorResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to refresh balance view: %v", err),
		}
	}

	return PayoutProcessorResponse{
		Success: true,
		Message: "Successfully refreshed company available balances materialized view",
	}
}
