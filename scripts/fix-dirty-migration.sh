#!/bin/bash
set -euo pipefail

echo "🔧 Fixing dirty migration state..."

# Check if migrate binary exists
if [ ! -f "./build/bin/migrate" ]; then
    echo "📥 Downloading migrate binary..."
    mkdir -p ./build/bin
    curl -s -L https://github.com/golang-migrate/migrate/releases/download/v4.14.1/migrate.linux-amd64.tar.gz | tar xz -C ./build/bin/
    mv ./build/bin/migrate.linux-amd64 ./build/bin/migrate
    chmod +x ./build/bin/migrate
fi

# Get database URL with SSL configuration
source scripts/migrate.sh
DEFAULT_DB_URL_SSL=$(add_ssl_to_url "$DEFAULT_DB_URL")

echo "🔍 Checking current migration status..."
MIGRATION_STATUS=$(./build/bin/migrate -path ./pkg/storage/postgres/migrations -database "$DEFAULT_DB_URL_SSL" version 2>&1 || echo "dirty")

if echo "$MIGRATION_STATUS" | grep -q "Dirty database version"; then
    DIRTY_VERSION=$(echo "$MIGRATION_STATUS" | grep -o "version [0-9]*" | grep -o "[0-9]*")
    echo "⚠️  Database is in dirty state at version $DIRTY_VERSION"
    
    echo "🔧 Forcing migration to clean state..."
    ./build/bin/migrate -path ./pkg/storage/postgres/migrations -database "$DEFAULT_DB_URL_SSL" force $DIRTY_VERSION
    
    echo "✅ Migration state cleaned. Current version: $DIRTY_VERSION"
    
    echo "🚀 Running migrations..."
    ./build/bin/migrate -path ./pkg/storage/postgres/migrations -database "$DEFAULT_DB_URL_SSL" -verbose up
    
    echo "✅ All migrations completed successfully!"
else
    echo "✅ Database is not in dirty state. Current status:"
    echo "$MIGRATION_STATUS"
fi
