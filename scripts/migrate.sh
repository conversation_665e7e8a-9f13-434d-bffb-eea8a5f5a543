#!/bin/bash
set -euo pipefail

if ! command -v psql &> /dev/null; then
    apt-get update && apt-get install -y postgresql-client
fi

TMPDIR=$(mktemp -d)
trap 'rm -rf "$TMPDIR"' EXIT

# Salva os certificados e chaves nas variáveis de ambiente para arquivos temporários (apenas se SSL estiver configurado)
if [ -n "${DATABASE_SSL_SERVER_CA:-}" ]; then
    echo "$DATABASE_SSL_SERVER_CA" > "$TMPDIR/server-ca.pem"
    chmod 600 "$TMPDIR/server-ca.pem"

    if [ -n "${DATABASE_SSL_CLIENT_CERT:-}" ] && [ -n "${DATABASE_SSL_CLIENT_KEY:-}" ]; then
        echo "$DATABASE_SSL_CLIENT_CERT" > "$TMPDIR/client-cert.pem"
        echo "$DATABASE_SSL_CLIENT_KEY" > "$TMPDIR/client-key.pem"
        chmod 600 "$TMPDIR/client-cert.pem" "$TMPDIR/client-key.pem"
    fi
fi

# Extrai nome do banco
DB_NAME=$(echo "$DATABASE_URL" | awk -F'/' '{print $NF}' | awk -F'?' '{print $1}')

# Constrói URL base para postgres (default DB)
DEFAULT_DB_URL=$(echo "$DATABASE_URL" | sed "s|/${DB_NAME}?|/postgres?|" | sed "s|/${DB_NAME}$|/postgres|")

# Acrescenta parâmetros SSL na URL para psql e migrate
add_ssl_to_url() {
    local url="$1"

    # Check if SSL configuration is available
    if [ -z "${DATABASE_SSL_SERVER_CA:-}" ]; then
        # No SSL configuration, ensure sslmode=disable if not already set
        if [[ "$url" != *"sslmode="* ]]; then
            if [[ "$url" == *"?"* ]]; then
                echo "${url}&sslmode=disable"
            else
                echo "${url}?sslmode=disable"
            fi
        else
            echo "$url"
        fi
        return
    fi

    # SSL is configured, use verify-ca mode
    local ssl_params="sslmode=verify-ca&sslrootcert=$TMPDIR/server-ca.pem"
    if [ -f "$TMPDIR/client-cert.pem" ] && [ -f "$TMPDIR/client-key.pem" ]; then
        ssl_params="$ssl_params&sslcert=$TMPDIR/client-cert.pem&sslkey=$TMPDIR/client-key.pem"
    fi

    if [[ "$url" == *"?"* ]]; then
        echo "${url}&${ssl_params}"
    else
        echo "${url}?${ssl_params}"
    fi
}

DEFAULT_DB_URL_SSL=$(add_ssl_to_url "$DEFAULT_DB_URL")

echo "Checking if database '$DB_NAME' exists..."
psql "$DEFAULT_DB_URL_SSL" -tc "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME'" | grep -q 1
if [ $? -ne 0 ]; then
    echo "Database '$DB_NAME' does not exist. Creating it..."
    psql "$DEFAULT_DB_URL_SSL" -c "CREATE DATABASE \"$DB_NAME\";"
else
    echo "Database '$DB_NAME' already exists."
fi

if [ "${APP_ENV:-}" = "development" ]; then
    DUMP_PATH=$(find ./bkp -type f -name "db_prod.sql" -printf "%T@ %p\n" | sort -nr | head -n1 | cut -d' ' -f2-)
    echo "Importing dump from $DUMP_PATH..."
    psql "$DEFAULT_DB_URL_SSL" < "$DUMP_PATH"
fi

mkdir -p ./build/bin
curl -s -L https://github.com/golang-migrate/migrate/releases/download/v4.14.1/migrate.linux-amd64.tar.gz | tar xz -C ./build/bin/
mv ./build/bin/migrate.linux-amd64 ./build/bin/migrate
chmod +x ./build/bin/migrate

# Check if database is in dirty state and fix it
echo "Checking migration status..."
MIGRATION_STATUS=$(./build/bin/migrate -path ./pkg/storage/postgres/migrations -database "$DEFAULT_DB_URL_SSL" version 2>&1 || echo "dirty")

if echo "$MIGRATION_STATUS" | grep -q "Dirty database version"; then
    DIRTY_VERSION=$(echo "$MIGRATION_STATUS" | grep -o "version [0-9]*" | grep -o "[0-9]*")
    echo "Database is in dirty state at version $DIRTY_VERSION. Forcing version to clean state..."
    ./build/bin/migrate -path ./pkg/storage/postgres/migrations -database "$DEFAULT_DB_URL_SSL" force $DIRTY_VERSION
    echo "Forced migration to clean state. Continuing with migrations..."
fi

./build/bin/migrate -path ./pkg/storage/postgres/migrations -database "$DEFAULT_DB_URL_SSL" -verbose up

psql "$DEFAULT_DB_URL_SSL" -c "SELECT setval('products_id_seq', COALESCE((SELECT MAX(id) FROM products), 0));"
psql "$DEFAULT_DB_URL_SSL" -c "SELECT setval('categories_id_seq', COALESCE((SELECT MAX(id) FROM categories), 0));"